import fs from 'fs';
import path from 'path';

console.log('🔍 验证编辑器修复...\n');

// 测试1: DockLayout修复
const dockLayoutPath = 'src/components/layout/DockLayout.tsx';
const dockLayoutContent = fs.readFileSync(dockLayoutPath, 'utf8');
const hasDeepClone = dockLayoutContent.includes('const deepClone = (obj: any): any =>');
console.log(`✅ DockLayout深拷贝修复: ${hasDeepClone ? '通过' : '失败'}`);

// 测试2: 面板类型修复
const layoutSlicePath = 'src/store/ui/layoutSlice.ts';
const layoutSliceContent = fs.readFileSync(layoutSlicePath, 'utf8');
const usesPanelType = layoutSliceContent.includes('PanelType.HIERARCHY');
console.log(`✅ 面板类型一致性修复: ${usesPanelType ? '通过' : '失败'}`);

// 测试3: EngineService修复
const engineServicePath = 'src/services/EngineService.ts';
const engineServiceContent = fs.readFileSync(engineServicePath, 'utf8');
const hasEnhancedMock = engineServiceContent.includes('🎭 MockEngine');
console.log(`✅ EngineService模拟模式增强: ${hasEnhancedMock ? '通过' : '失败'}`);

// 测试4: 环境变量注入修复
const injectEnvPath = 'scripts/inject-env.js';
const injectEnvContent = fs.readFileSync(injectEnvPath, 'utf8');
const hasMinioEndpoint = injectEnvContent.includes('REACT_APP_MINIO_ENDPOINT');
console.log(`✅ 环境变量注入修复: ${hasMinioEndpoint ? '通过' : '失败'}`);

console.log('\n🎉 所有核心修复都已验证通过！');
