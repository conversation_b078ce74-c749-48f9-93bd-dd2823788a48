#!/usr/bin/env node

/**
 * Git冲突窗口修复验证脚本
 * 验证GitConflictResolver组件的显示控制和翻译键修复
 */

const fs = require('fs');
const path = require('path');

let allTestsPassed = true;

// 辅助函数：检查文件是否存在
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} - 文件不存在`);
    allTestsPassed = false;
    return false;
  }
}

// 辅助函数：检查文件内容
function checkFileContent(filePath, pattern, description) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ ${description}: ${filePath} - 文件不存在`);
      allTestsPassed = false;
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const matches = pattern.test(content);
    
    if (matches) {
      console.log(`✅ ${description}: 已修复`);
      return true;
    } else {
      console.log(`❌ ${description}: 未找到预期内容`);
      allTestsPassed = false;
      return false;
    }
  } catch (error) {
    console.log(`❌ ${description}: 检查失败 - ${error.message}`);
    allTestsPassed = false;
    return false;
  }
}

console.log('🔍 开始验证Git冲突窗口修复...\n');

// 1. 验证GitConflictResolver组件修复
console.log('1. 验证GitConflictResolver组件修复...');

// 检查组件文件存在
checkFileExists('editor/src/components/git/GitConflictResolver.tsx', 'GitConflictResolver组件文件');

// 检查显示条件控制
checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /if \(!showConflictPanel \|\| mergeConflicts\.length === 0\) \{[\s\S]*?return null;[\s\S]*?\}/,
  'GitConflictResolver显示条件控制'
);

// 检查selectShowConflictPanel导入
checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /import.*selectShowConflictPanel.*from.*gitSlice/,
  'selectShowConflictPanel导入'
);

// 检查showConflictPanel状态使用
checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /const showConflictPanel = useSelector\(selectShowConflictPanel\)/,
  'showConflictPanel状态获取'
);

// 检查固定定位样式
checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /position: 'fixed'/,
  'GitConflictResolver固定定位样式'
);

console.log('\n2. 验证App.tsx中的状态清理...');

// 检查Git状态清理导入
checkFileContent(
  'editor/src/App.tsx',
  /import.*setShowConflictPanel as setGitShowConflictPanel.*setMergeConflicts.*from.*gitSlice/,
  'Git状态管理函数导入'
);

// 检查初始化时的状态清理
checkFileContent(
  'editor/src/App.tsx',
  /dispatch\(setGitShowConflictPanel\(false\)\);[\s\S]*?dispatch\(setMergeConflicts\(\[\]\)\);/,
  '应用初始化时Git状态清理'
);

// 检查路径变化时的状态清理
checkFileContent(
  'editor/src/App.tsx',
  /dispatch\(setGitShowConflictPanel\(false\)\);[\s\S]*?dispatch\(setMergeConflicts\(\[\]\)\);/,
  '路径变化时Git状态清理'
);

console.log('\n3. 验证翻译键修复...');

// 检查中文翻译文件
checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"noConflicts":\s*"没有冲突"/,
  '中文翻译 - noConflicts'
);

checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"previous":\s*"上一个"/,
  '中文翻译 - previous'
);

checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"next":\s*"下一个"/,
  '中文翻译 - next'
);

checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"resolve":\s*"解决"/,
  '中文翻译 - resolve'
);

checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"confirmCloseContent":\s*"确定要关闭冲突解决面板吗？未解决的冲突将保留。"/,
  '中文翻译 - confirmCloseContent'
);

// 检查英文翻译文件
checkFileContent(
  'editor/src/i18n/locales/en-US.json',
  /"noConflicts":\s*"No conflicts"/,
  '英文翻译 - noConflicts'
);

checkFileContent(
  'editor/src/i18n/locales/en-US.json',
  /"previous":\s*"Previous"/,
  '英文翻译 - previous'
);

checkFileContent(
  'editor/src/i18n/locales/en-US.json',
  /"next":\s*"Next"/,
  '英文翻译 - next'
);

checkFileContent(
  'editor/src/i18n/locales/en-US.json',
  /"resolve":\s*"Resolve"/,
  '英文翻译 - resolve'
);

console.log('\n4. 验证配置文件一致性...');

// 检查.env文件
checkFileExists('.env', '环境配置文件');

// 检查docker-compose.windows.yml
checkFileExists('docker-compose.windows.yml', 'Docker Compose配置文件');

// 检查编辑器Dockerfile
checkFileExists('editor/Dockerfile', '编辑器Dockerfile');

// 检查start-windows.ps1
checkFileExists('start-windows.ps1', 'Windows启动脚本');

// 检查stop-windows.ps1
checkFileExists('stop-windows.ps1', 'Windows停止脚本');

console.log('\n5. 验证组件翻译键使用...');

// 检查组件中的翻译键回退值
checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /t\('git\.resolveConflicts'\) \|\| '解决冲突'/,
  '解决冲突标题翻译回退'
);

checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /t\('git\.noConflicts'\) \|\| '没有冲突'/,
  '没有冲突翻译回退'
);

checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /t\('git\.previous'\) \|\| '上一个'/,
  '上一个按钮翻译回退'
);

checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /t\('git\.next'\) \|\| '下一个'/,
  '下一个按钮翻译回退'
);

checkFileContent(
  'editor/src/components/git/GitConflictResolver.tsx',
  /t\('git\.resolve'\) \|\| '解决'/,
  '解决按钮翻译回退'
);

console.log('\n📋 修复验证总结:');
console.log('=====================================');

if (allTestsPassed) {
  console.log('🎉 所有测试通过！Git冲突窗口修复成功。');
  console.log('\n✅ 修复内容:');
  console.log('  - GitConflictResolver组件添加了显示条件控制');
  console.log('  - 只有在showConflictPanel为true且有冲突时才显示');
  console.log('  - 应用初始化时清除Git冲突状态');
  console.log('  - 路径变化时清除Git冲突状态');
  console.log('  - 添加了完整的Git翻译键');
  console.log('  - 组件使用固定定位，确保正确显示');
  console.log('  - 配置文件保持一致性');
  
  console.log('\n🔧 预期效果:');
  console.log('  - 登录后不会自动显示冲突窗口');
  console.log('  - 只有在实际发生Git冲突时才显示');
  console.log('  - 窗口可以正常关闭');
  console.log('  - 所有文本显示为中文');
  console.log('  - 按钮功能正常工作');
  
  process.exit(0);
} else {
  console.log('❌ 部分测试失败，请检查上述问题。');
  process.exit(1);
}
