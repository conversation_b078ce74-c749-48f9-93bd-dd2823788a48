#!/usr/bin/env node

/**
 * 编辑器修复验证脚本
 * 用于测试修复后的编辑器功能
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 测试结果
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

/**
 * 测试工具函数
 */
function test(name, testFn) {
  try {
    console.log(`🧪 测试: ${name}`);
    const result = testFn();
    if (result) {
      console.log(`✅ 通过: ${name}`);
      testResults.passed++;
    } else {
      console.log(`❌ 失败: ${name}`);
      testResults.failed++;
      testResults.errors.push(name);
    }
  } catch (error) {
    console.log(`❌ 错误: ${name} - ${error.message}`);
    testResults.failed++;
    testResults.errors.push(`${name}: ${error.message}`);
  }
}

/**
 * 测试1: 检查DockLayout组件修复
 */
test('DockLayout组件深拷贝修复', () => {
  const dockLayoutPath = path.join(__dirname, 'src/components/layout/DockLayout.tsx');
  const content = fs.readFileSync(dockLayoutPath, 'utf8');
  
  // 检查是否包含深拷贝逻辑
  const hasDeepClone = content.includes('const deepClone = (obj: any): any =>');
  const hasClonedChild = content.includes('const clonedChild = deepClone(child)');
  
  return hasDeepClone && hasClonedChild;
});

/**
 * 测试2: 检查面板类型一致性
 */
test('面板类型一致性修复', () => {
  const layoutSlicePath = path.join(__dirname, 'src/store/ui/layoutSlice.ts');
  const content = fs.readFileSync(layoutSlicePath, 'utf8');
  
  // 检查是否使用PanelType枚举
  const usesPanelType = content.includes('PanelType.HIERARCHY') && 
                       content.includes('PanelType.SCENE') &&
                       content.includes('PanelType.INSPECTOR');
  
  return usesPanelType;
});

/**
 * 测试3: 检查EngineService模拟模式
 */
test('EngineService模拟模式增强', () => {
  const engineServicePath = path.join(__dirname, 'src/services/EngineService.ts');
  const content = fs.readFileSync(engineServicePath, 'utf8');
  
  // 检查是否包含增强的MockEngine
  const hasEnhancedMock = content.includes('drawMockScene') && 
                         content.includes('startRenderLoop') &&
                         content.includes('🎭 MockEngine');
  
  return hasEnhancedMock;
});

/**
 * 测试4: 检查环境变量注入脚本
 */
test('环境变量注入脚本修复', () => {
  const injectEnvPath = path.join(__dirname, 'scripts/inject-env.js');
  const content = fs.readFileSync(injectEnvPath, 'utf8');
  
  // 检查是否包含所有必要的环境变量
  const hasMinioEndpoint = content.includes('REACT_APP_MINIO_ENDPOINT');
  const hasEnvironment = content.includes('REACT_APP_ENVIRONMENT');
  const hasVersion = content.includes('REACT_APP_VERSION');
  
  return hasMinioEndpoint && hasEnvironment && hasVersion;
});

/**
 * 测试5: 检查配置文件一致性
 */
test('Docker配置文件一致性', () => {
  const dockerfilePath = path.join(__dirname, 'Dockerfile');
  const dockerComposeContent = fs.readFileSync(path.join(__dirname, '../docker-compose.windows.yml'), 'utf8');
  const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
  
  // 检查环境变量是否一致
  const envVars = [
    'REACT_APP_API_URL=/api',
    'REACT_APP_COLLABORATION_SERVER_URL=/ws',
    'REACT_APP_MINIO_ENDPOINT=http://localhost:9000',
    'REACT_APP_ENVIRONMENT=production'
  ];
  
  const dockerfileHasVars = envVars.every(envVar => dockerfileContent.includes(envVar));
  const dockerComposeHasVars = envVars.every(envVar => dockerComposeContent.includes(envVar));
  
  return dockerfileHasVars && dockerComposeHasVars;
});

/**
 * 测试6: 检查必要文件存在性
 */
test('必要文件存在性检查', () => {
  const requiredFiles = [
    'src/components/layout/DockLayout.tsx',
    'src/components/panels/PanelRegistry.tsx',
    'src/services/EngineService.ts',
    'src/store/ui/layoutSlice.ts',
    'scripts/inject-env.js',
    'nginx.conf',
    'Dockerfile'
  ];
  
  return requiredFiles.every(file => {
    const filePath = path.join(__dirname, file);
    return fs.existsSync(filePath);
  });
});

/**
 * 测试7: 检查TypeScript类型定义
 */
test('TypeScript类型定义检查', () => {
  const typesPath = path.join(__dirname, 'src/libs/types/index.d.ts');
  
  if (!fs.existsSync(typesPath)) {
    console.log('⚠️ 类型定义文件不存在，但这不是致命错误');
    return true; // 不是致命错误
  }
  
  const content = fs.readFileSync(typesPath, 'utf8');
  return content.length > 0;
});

/**
 * 测试8: 检查构建配置
 */
test('构建配置检查', () => {
  const viteConfigPath = path.join(__dirname, 'vite.config.ts');
  const packageJsonPath = path.join(__dirname, 'package.json');
  
  const viteConfigExists = fs.existsSync(viteConfigPath);
  const packageJsonExists = fs.existsSync(packageJsonPath);
  
  if (packageJsonExists) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const hasBuildScript = packageJson.scripts && packageJson.scripts.build;
    const hasInjectEnvScript = packageJson.scripts && packageJson.scripts['inject-env'];
    
    return viteConfigExists && hasBuildScript && hasInjectEnvScript;
  }
  
  return false;
});

/**
 * 运行所有测试
 */
function runTests() {
  console.log('🚀 开始运行编辑器修复验证测试...\n');
  
  // 运行所有测试
  test('DockLayout组件深拷贝修复', () => {
    const dockLayoutPath = path.join(__dirname, 'src/components/layout/DockLayout.tsx');
    const content = fs.readFileSync(dockLayoutPath, 'utf8');
    const hasDeepClone = content.includes('const deepClone = (obj: any): any =>');
    const hasClonedChild = content.includes('const clonedChild = deepClone(child)');
    return hasDeepClone && hasClonedChild;
  });
  
  test('面板类型一致性修复', () => {
    const layoutSlicePath = path.join(__dirname, 'src/store/ui/layoutSlice.ts');
    const content = fs.readFileSync(layoutSlicePath, 'utf8');
    const usesPanelType = content.includes('PanelType.HIERARCHY') && 
                         content.includes('PanelType.SCENE') &&
                         content.includes('PanelType.INSPECTOR');
    return usesPanelType;
  });
  
  test('EngineService模拟模式增强', () => {
    const engineServicePath = path.join(__dirname, 'src/services/EngineService.ts');
    const content = fs.readFileSync(engineServicePath, 'utf8');
    const hasEnhancedMock = content.includes('drawMockScene') && 
                           content.includes('startRenderLoop') &&
                           content.includes('🎭 MockEngine');
    return hasEnhancedMock;
  });
  
  test('环境变量注入脚本修复', () => {
    const injectEnvPath = path.join(__dirname, 'scripts/inject-env.js');
    const content = fs.readFileSync(injectEnvPath, 'utf8');
    const hasMinioEndpoint = content.includes('REACT_APP_MINIO_ENDPOINT');
    const hasEnvironment = content.includes('REACT_APP_ENVIRONMENT');
    const hasVersion = content.includes('REACT_APP_VERSION');
    return hasMinioEndpoint && hasEnvironment && hasVersion;
  });
  
  test('必要文件存在性检查', () => {
    const requiredFiles = [
      'src/components/layout/DockLayout.tsx',
      'src/components/panels/PanelRegistry.tsx',
      'src/services/EngineService.ts',
      'src/store/ui/layoutSlice.ts',
      'scripts/inject-env.js',
      'nginx.conf',
      'Dockerfile'
    ];
    return requiredFiles.every(file => {
      const filePath = path.join(__dirname, file);
      return fs.existsSync(filePath);
    });
  });
  
  test('构建配置检查', () => {
    const viteConfigPath = path.join(__dirname, 'vite.config.ts');
    const packageJsonPath = path.join(__dirname, 'package.json');
    const viteConfigExists = fs.existsSync(viteConfigPath);
    const packageJsonExists = fs.existsSync(packageJsonPath);
    
    if (packageJsonExists) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const hasBuildScript = packageJson.scripts && packageJson.scripts.build;
      const hasInjectEnvScript = packageJson.scripts && packageJson.scripts['inject-env'];
      return viteConfigExists && hasBuildScript && hasInjectEnvScript;
    }
    return false;
  });
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach(error => {
      console.log(`  - ${error}`);
    });
  }
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有测试都通过了！编辑器修复成功。');
    return true;
  } else {
    console.log('\n⚠️ 有测试失败，请检查相关问题。');
    return false;
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

export { runTests, testResults };
