#!/usr/bin/env node

/**
 * 编辑器布局修复验证脚本
 * 验证项目卡片点击后是否进入完整编辑器环境
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 编辑器布局修复验证');
console.log('=' * 50);

// 检查修复点1：EditorPage.tsx中的布局选择逻辑
function checkEditorPageFix() {
  console.log('\n📋 检查1: EditorPage.tsx布局选择逻辑');
  
  const editorPagePath = path.join(__dirname, 'editor/src/pages/EditorPage.tsx');
  
  if (!fs.existsSync(editorPagePath)) {
    console.log('❌ EditorPage.tsx文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(editorPagePath, 'utf8');
  
  // 检查是否修复了硬编码的useSimpleLayout
  if (content.includes('const useSimpleLayout = true')) {
    console.log('❌ 仍然硬编码使用简化布局');
    return false;
  }
  
  if (content.includes('const useSimpleLayout = false')) {
    console.log('✅ 已修复：默认使用完整编辑器布局');
  } else {
    console.log('⚠️  未找到useSimpleLayout变量定义');
  }
  
  // 检查是否支持URL参数调试模式
  if (content.includes('urlParams.get(\'debug\')')) {
    console.log('✅ 已添加：支持URL参数调试模式');
  } else {
    console.log('⚠️  未找到URL参数调试模式支持');
  }
  
  return true;
}

// 检查修复点2：DockLayout.tsx中的tabRenderer
function checkDockLayoutFix() {
  console.log('\n📋 检查2: DockLayout.tsx面板内容渲染');
  
  const dockLayoutPath = path.join(__dirname, 'editor/src/components/layout/DockLayout.tsx');
  
  if (!fs.existsSync(dockLayoutPath)) {
    console.log('❌ DockLayout.tsx文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(dockLayoutPath, 'utf8');
  
  // 检查是否导入了createPanelContent
  if (content.includes('import { createPanelContent }')) {
    console.log('✅ 已导入：createPanelContent函数');
  } else {
    console.log('❌ 缺少createPanelContent导入');
    return false;
  }
  
  // 检查是否使用了processedLayout处理
  if (content.includes('defaultLayout={processedLayout}')) {
    console.log('✅ 已修复：使用processedLayout处理布局');
  } else {
    console.log('❌ 缺少processedLayout处理');
    return false;
  }

  // 检查React.useMemo优化
  if (content.includes('React.useMemo')) {
    console.log('✅ 已优化：使用React.useMemo缓存布局处理');
  } else {
    console.log('❌ 缺少React.useMemo优化');
    return false;
  }

  // 检查是否移除了不支持的tabRenderer属性
  if (!content.includes('tabRenderer={')) {
    console.log('✅ 已修复：移除了不支持的tabRenderer属性');
  } else {
    console.log('❌ 仍然使用不支持的tabRenderer属性');
    return false;
  }
  
  return true;
}

// 检查配置文件一致性
function checkConfigConsistency() {
  console.log('\n📋 检查3: 配置文件一致性');
  
  const configFiles = [
    '.env',
    'docker-compose.windows.yml',
    'start-windows.ps1',
    'stop-windows.ps1'
  ];
  
  let allExist = true;
  
  configFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} 存在`);
    } else {
      console.log(`❌ ${file} 不存在`);
      allExist = false;
    }
  });
  
  return allExist;
}

// 检查Dockerfile一致性
function checkDockerfileConsistency() {
  console.log('\n📋 检查4: Dockerfile配置一致性');
  
  const dockerfiles = [
    'editor/Dockerfile',
    'server/api-gateway/Dockerfile',
    'server/user-service/Dockerfile',
    'server/project-service/Dockerfile',
    'server/asset-service/Dockerfile',
    'server/service-registry/Dockerfile'
  ];
  
  let allExist = true;
  
  dockerfiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} 存在`);
      
      // 检查是否使用了一致的基础镜像
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('FROM node:22-alpine')) {
        console.log(`  ✅ 使用一致的基础镜像 (node:22-alpine)`);
      } else {
        console.log(`  ⚠️  基础镜像可能不一致`);
      }
    } else {
      console.log(`❌ ${file} 不存在`);
      allExist = false;
    }
  });
  
  return allExist;
}

// 生成修复报告
function generateReport() {
  console.log('\n📊 修复验证报告');
  console.log('=' * 50);
  
  const checks = [
    { name: 'EditorPage布局选择逻辑', result: checkEditorPageFix() },
    { name: 'DockLayout面板内容渲染', result: checkDockLayoutFix() },
    { name: '配置文件一致性', result: checkConfigConsistency() },
    { name: 'Dockerfile配置一致性', result: checkDockerfileConsistency() }
  ];
  
  const passedChecks = checks.filter(check => check.result).length;
  const totalChecks = checks.length;
  
  console.log(`\n总体状态: ${passedChecks}/${totalChecks} 项检查通过`);
  
  if (passedChecks === totalChecks) {
    console.log('🎉 所有修复验证通过！');
    console.log('\n✅ 修复总结:');
    console.log('1. 修复了EditorPage.tsx中硬编码使用简化布局的问题');
    console.log('2. 修复了DockLayout.tsx中面板内容无法正确渲染的问题');
    console.log('3. 确保了配置文件的一致性');
    console.log('4. 验证了Dockerfile配置的一致性');
    console.log('\n🚀 现在用户点击项目卡片应该能进入完整的编辑器环境！');
  } else {
    console.log('⚠️  部分修复验证未通过，请检查上述问题');
  }
  
  return passedChecks === totalChecks;
}

// 主函数
function main() {
  try {
    const success = generateReport();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkEditorPageFix,
  checkDockLayoutFix,
  checkConfigConsistency,
  checkDockerfileConsistency,
  generateReport
};
