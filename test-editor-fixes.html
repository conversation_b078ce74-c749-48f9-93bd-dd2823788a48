<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑器修复验证测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.3em;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .status {
            padding: 10px 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }
        
        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }
        
        .info-card h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 编辑器修复验证测试</h1>
            <p>验证项目卡片点击功能和编辑器加载修复效果</p>
        </div>
        
        <div class="content">
            <div class="info-grid">
                <div class="info-card">
                    <h4>📋 测试信息</h4>
                    <p><strong>测试时间:</strong> <span id="testTime"></span></p>
                    <p><strong>浏览器:</strong> <span id="browserInfo"></span></p>
                    <p><strong>测试状态:</strong> <span id="testStatus">准备就绪</span></p>
                </div>
                <div class="info-card">
                    <h4>🔗 相关链接</h4>
                    <p><a href="http://localhost/" target="_blank">前端编辑器首页</a></p>
                    <p><a href="http://localhost/projects" target="_blank">项目管理页面</a></p>
                    <p><a href="http://localhost:3000/api/health" target="_blank">API健康检查</a></p>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🎯 1. 基础服务连通性测试</h3>
                <p>测试前端服务、API网关和后端服务的连通性</p>
                <button class="test-button" onclick="testBasicConnectivity()">开始测试</button>
                <div id="connectivityStatus"></div>
                <div class="progress-bar">
                    <div id="connectivityProgress" class="progress-fill"></div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🔐 2. 用户认证测试</h3>
                <p>测试用户登录状态和认证功能</p>
                <button class="test-button" onclick="testAuthentication()">测试认证</button>
                <div id="authStatus"></div>
                <div class="progress-bar">
                    <div id="authProgress" class="progress-fill"></div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📁 3. 项目数据获取测试</h3>
                <p>测试项目列表获取和项目详情API</p>
                <button class="test-button" onclick="testProjectData()">测试项目数据</button>
                <div id="projectStatus"></div>
                <div class="progress-bar">
                    <div id="projectProgress" class="progress-fill"></div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🎮 4. 编辑器加载测试</h3>
                <p>测试编辑器页面加载和简化布局功能</p>
                <button class="test-button" onclick="testEditorLoading()">测试编辑器加载</button>
                <button class="test-button" onclick="openSimpleEditor()">打开简化编辑器</button>
                <div id="editorStatus"></div>
                <div class="progress-bar">
                    <div id="editorProgress" class="progress-fill"></div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🚀 5. 完整流程测试</h3>
                <p>模拟完整的项目卡片点击到编辑器打开流程</p>
                <button class="test-button" onclick="testCompleteFlow()">完整流程测试</button>
                <div id="flowStatus"></div>
                <div class="progress-bar">
                    <div id="flowProgress" class="progress-fill"></div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📊 测试日志</h3>
                <div id="testLog" class="log"></div>
                <button class="test-button" onclick="clearLog()">清空日志</button>
                <button class="test-button" onclick="exportLog()">导出日志</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化页面
        document.getElementById('testTime').textContent = new Date().toLocaleString();
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
        
        // 日志功能
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function setProgress(elementId, percentage) {
            const element = document.getElementById(elementId);
            element.style.width = `${percentage}%`;
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
        
        function exportLog() {
            const logContent = document.getElementById('testLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `editor-test-log-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 测试函数
        async function testBasicConnectivity() {
            log('开始基础服务连通性测试', 'info');
            setStatus('connectivityStatus', '正在测试...', 'info');
            setProgress('connectivityProgress', 0);
            
            try {
                // 测试前端服务
                log('测试前端服务连通性...', 'info');
                setProgress('connectivityProgress', 25);
                const frontendResponse = await fetch('http://localhost/');
                if (!frontendResponse.ok) throw new Error(`前端服务响应错误: ${frontendResponse.status}`);
                log('✅ 前端服务连通正常', 'success');
                
                // 测试API网关
                log('测试API网关连通性...', 'info');
                setProgress('connectivityProgress', 50);
                const apiResponse = await fetch('http://localhost/api/health');
                if (!apiResponse.ok) throw new Error(`API网关响应错误: ${apiResponse.status}`);
                log('✅ API网关连通正常', 'success');
                
                // 测试静态资源
                log('测试静态资源加载...', 'info');
                setProgress('connectivityProgress', 75);
                const assetsResponse = await fetch('http://localhost/favicon.svg');
                if (!assetsResponse.ok) throw new Error(`静态资源加载错误: ${assetsResponse.status}`);
                log('✅ 静态资源加载正常', 'success');
                
                setProgress('connectivityProgress', 100);
                setStatus('connectivityStatus', '✅ 所有基础服务连通正常', 'success');
                log('基础服务连通性测试完成', 'success');
                
            } catch (error) {
                log(`❌ 基础服务连通性测试失败: ${error.message}`, 'error');
                setStatus('connectivityStatus', `❌ 测试失败: ${error.message}`, 'error');
                setProgress('connectivityProgress', 0);
            }
        }
        
        async function testAuthentication() {
            log('开始用户认证测试', 'info');
            setStatus('authStatus', '正在测试...', 'info');
            setProgress('authProgress', 0);
            
            try {
                // 检查当前认证状态
                log('检查当前认证状态...', 'info');
                setProgress('authProgress', 50);
                
                const authResponse = await fetch('http://localhost/api/auth/profile', {
                    credentials: 'include'
                });
                
                if (authResponse.ok) {
                    const userInfo = await authResponse.json();
                    log(`✅ 用户已认证: ${userInfo.username || '未知用户'}`, 'success');
                    setStatus('authStatus', `✅ 用户已认证: ${userInfo.username || '未知用户'}`, 'success');
                } else if (authResponse.status === 401) {
                    log('⚠️ 用户未认证，需要登录', 'warning');
                    setStatus('authStatus', '⚠️ 用户未认证，请先登录', 'error');
                } else {
                    throw new Error(`认证检查失败: ${authResponse.status}`);
                }
                
                setProgress('authProgress', 100);
                log('用户认证测试完成', 'success');
                
            } catch (error) {
                log(`❌ 用户认证测试失败: ${error.message}`, 'error');
                setStatus('authStatus', `❌ 测试失败: ${error.message}`, 'error');
                setProgress('authProgress', 0);
            }
        }
        
        async function testProjectData() {
            log('开始项目数据获取测试', 'info');
            setStatus('projectStatus', '正在测试...', 'info');
            setProgress('projectProgress', 0);
            
            try {
                // 获取项目列表
                log('获取项目列表...', 'info');
                setProgress('projectProgress', 33);
                
                const projectsResponse = await fetch('http://localhost/api/projects', {
                    credentials: 'include'
                });
                
                if (!projectsResponse.ok) {
                    throw new Error(`项目列表获取失败: ${projectsResponse.status}`);
                }
                
                const projects = await projectsResponse.json();
                log(`✅ 成功获取 ${projects.length} 个项目`, 'success');
                
                if (projects.length > 0) {
                    const firstProject = projects[0];
                    log(`测试项目: ${firstProject.name} (ID: ${firstProject.id})`, 'info');
                    setProgress('projectProgress', 66);
                    
                    // 获取项目详情
                    const projectDetailResponse = await fetch(`http://localhost/api/projects/${firstProject.id}`, {
                        credentials: 'include'
                    });
                    
                    if (!projectDetailResponse.ok) {
                        throw new Error(`项目详情获取失败: ${projectDetailResponse.status}`);
                    }
                    
                    const projectDetail = await projectDetailResponse.json();
                    log(`✅ 成功获取项目详情: ${projectDetail.name}`, 'success');
                    
                    // 获取项目场景
                    const scenesResponse = await fetch(`http://localhost/api/projects/${firstProject.id}/scenes`, {
                        credentials: 'include'
                    });
                    
                    if (scenesResponse.ok) {
                        const scenes = await scenesResponse.json();
                        log(`✅ 成功获取 ${scenes.length} 个场景`, 'success');
                    } else {
                        log(`⚠️ 场景获取失败: ${scenesResponse.status}`, 'warning');
                    }
                }
                
                setProgress('projectProgress', 100);
                setStatus('projectStatus', '✅ 项目数据获取正常', 'success');
                log('项目数据获取测试完成', 'success');
                
            } catch (error) {
                log(`❌ 项目数据获取测试失败: ${error.message}`, 'error');
                setStatus('projectStatus', `❌ 测试失败: ${error.message}`, 'error');
                setProgress('projectProgress', 0);
            }
        }
        
        async function testEditorLoading() {
            log('开始编辑器加载测试', 'info');
            setStatus('editorStatus', '正在测试...', 'info');
            setProgress('editorProgress', 0);
            
            try {
                // 测试编辑器页面访问
                log('测试编辑器页面访问...', 'info');
                setProgress('editorProgress', 50);
                
                // 创建一个隐藏的iframe来测试编辑器页面
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = 'http://localhost/editor/test-project/test-scene';
                
                const loadPromise = new Promise((resolve, reject) => {
                    iframe.onload = () => {
                        try {
                            // 检查iframe是否成功加载
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            if (iframeDoc.title.includes('错误') || iframeDoc.body.textContent.includes('加载失败')) {
                                reject(new Error('编辑器页面加载失败'));
                            } else {
                                resolve('编辑器页面加载成功');
                            }
                        } catch (e) {
                            // 跨域限制，但iframe加载成功
                            resolve('编辑器页面加载成功（跨域限制）');
                        }
                    };
                    iframe.onerror = () => reject(new Error('编辑器页面加载失败'));
                    
                    // 5秒超时
                    setTimeout(() => reject(new Error('编辑器页面加载超时')), 5000);
                });
                
                document.body.appendChild(iframe);
                
                try {
                    const result = await loadPromise;
                    log(`✅ ${result}`, 'success');
                    setStatus('editorStatus', '✅ 编辑器页面加载正常', 'success');
                } finally {
                    document.body.removeChild(iframe);
                }
                
                setProgress('editorProgress', 100);
                log('编辑器加载测试完成', 'success');
                
            } catch (error) {
                log(`❌ 编辑器加载测试失败: ${error.message}`, 'error');
                setStatus('editorStatus', `❌ 测试失败: ${error.message}`, 'error');
                setProgress('editorProgress', 0);
            }
        }
        
        function openSimpleEditor() {
            log('打开简化编辑器...', 'info');
            window.open('http://localhost/editor/test-project/test-scene', '_blank');
        }
        
        async function testCompleteFlow() {
            log('开始完整流程测试', 'info');
            setStatus('flowStatus', '正在测试...', 'info');
            setProgress('flowProgress', 0);
            
            try {
                // 步骤1: 基础连通性
                log('步骤1: 检查基础连通性...', 'info');
                setProgress('flowProgress', 20);
                await testBasicConnectivity();
                
                // 步骤2: 认证状态
                log('步骤2: 检查认证状态...', 'info');
                setProgress('flowProgress', 40);
                await testAuthentication();
                
                // 步骤3: 项目数据
                log('步骤3: 获取项目数据...', 'info');
                setProgress('flowProgress', 60);
                await testProjectData();
                
                // 步骤4: 编辑器加载
                log('步骤4: 测试编辑器加载...', 'info');
                setProgress('flowProgress', 80);
                await testEditorLoading();
                
                setProgress('flowProgress', 100);
                setStatus('flowStatus', '✅ 完整流程测试通过', 'success');
                log('完整流程测试完成', 'success');
                
            } catch (error) {
                log(`❌ 完整流程测试失败: ${error.message}`, 'error');
                setStatus('flowStatus', `❌ 测试失败: ${error.message}`, 'error');
                setProgress('flowProgress', 0);
            }
        }
        
        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动测试', 'info');
            setTimeout(() => {
                testBasicConnectivity();
            }, 1000);
        });
    </script>
</body>
</html>
